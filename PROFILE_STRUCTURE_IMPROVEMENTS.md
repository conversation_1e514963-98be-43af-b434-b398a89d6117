# Profile Structure Improvements

## Overview
Perbaikan struktur profile untuk mengatasi masalah duplikasi field school dan meningkatkan konsistensi API response.

## Masalah yang Diperbaiki

### 1. **Duplikasi Field School**
**Masalah Sebelumnya:**
- Profile memiliki field `school_origin` (string manual) dan `school_id` (foreign key)
- Response API menampilkan kedua field + object `school` dari relasi
- Membingungkan client karena ada 3 cara untuk mendapatkan informasi sekolah

**Solusi:**
- Validasi untuk memastikan hanya salah satu field yang digunakan
- Penambahan field `school_info` yang menggabungkan informasi dengan metadata
- Dokumentasi yang jelas tentang penggunaan field

### 2. **Duplikasi Response pada Job Archive**
**Masalah Sebelumnya:**
- Output test menunjukkan response yang berulang-ulang
- Kemungkinan masalah di client side atau terminal output

**Solusi:**
- <PERSON><PERSON><PERSON><PERSON> check `res.headersSent` untuk mencegah multiple response
- Perbaikan test client untuk output yang lebih bersih
- Debug mode untuk response detail

## Perubahan yang Dilakukan

### 1. **Validation Schema (auth-service/src/middleware/validation.js)**
```javascript
// Custom validation untuk mencegah konflik school fields
.custom((value, helpers) => {
  const { school_origin, school_id } = value;
  
  if (school_origin && school_id) {
    return helpers.error('custom.schoolConflict');
  }
  
  return value;
})
```

### 2. **Controller Logic (auth-service/src/controllers/userController.js)**
```javascript
// Clear conflicting school field
if (school_id !== undefined) {
  profileUpdateData.school_origin = null;
} else if (school_origin !== undefined) {
  profileUpdateData.school_id = null;
}
```

### 3. **Response Formatter**
```javascript
// Add school_info field yang menggabungkan informasi
userObj.profile.school_info = {
  type: userObj.profile.school_id ? 'structured' : 'manual',
  school_id: userObj.profile.school_id,
  school_origin: userObj.profile.school_origin,
  school: userObj.profile.school || null
};
```

### 4. **Archive Service Response Protection**
```javascript
// Prevent multiple response sending
if (res.headersSent) {
  console.warn('Attempted to send response after headers were already sent');
  return;
}
```

## API Response Structure

### Before (Confusing)
```json
{
  "profile": {
    "school_origin": "SMA Manual",
    "school_id": 123,
    "school": {
      "id": 123,
      "name": "SMA Negeri 1"
    }
  }
}
```

### After (Clear)
```json
{
  "profile": {
    "school_origin": null,
    "school_id": 123,
    "school": {
      "id": 123,
      "name": "SMA Negeri 1"
    },
    "school_info": {
      "type": "structured",
      "school_id": 123,
      "school_origin": null,
      "school": {
        "id": 123,
        "name": "SMA Negeri 1"
      }
    }
  }
}
```

## Usage Guidelines

### 1. **Untuk Client Applications**
- **Gunakan `school_info.type`** untuk menentukan jenis data sekolah
- **Jika type = "structured"**: Gunakan `school_info.school` untuk data lengkap
- **Jika type = "manual"**: Gunakan `school_info.school_origin` untuk nama manual

### 2. **Untuk Update Profile**
- **Structured Data**: Kirim `school_id` saja
- **Manual Input**: Kirim `school_origin` saja
- **Jangan kirim keduanya** - akan error validation

### 3. **Backward Compatibility**
- Field lama (`school_origin`, `school_id`, `school`) tetap ada
- Client lama masih bisa berfungsi
- Recommended untuk migrate ke `school_info`

## Testing

### Test Profile Update
```bash
# Test dengan school_id
curl -X PUT /api/auth/profile \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"school_id": 123}'

# Test dengan school_origin
curl -X PUT /api/auth/profile \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"school_origin": "SMA Manual"}'

# Test error - keduanya (should fail)
curl -X PUT /api/auth/profile \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"school_id": 123, "school_origin": "SMA Manual"}'
```

### Test Job Archive (Clean Output)
```bash
# Normal mode
node test-user-flow.js --websocket

# Debug mode (full response)
DEBUG_RESPONSE=true node test-user-flow.js --websocket
```

## Migration Notes

### For Existing Data
- Data existing tidak perlu diubah
- Field `school_info` akan di-generate otomatis
- Tidak ada breaking changes

### For New Features
- Gunakan `school_info` untuk logic baru
- Validate menggunakan `school_info.type`
- Handle both cases (structured vs manual)

## Benefits

1. **Consistency**: Struktur response yang konsisten
2. **Clarity**: Jelas field mana yang digunakan
3. **Flexibility**: Support both structured dan manual input
4. **Backward Compatible**: Tidak break existing clients
5. **Better UX**: Client tidak bingung dengan multiple fields
