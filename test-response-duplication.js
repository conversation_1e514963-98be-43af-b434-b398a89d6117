const axios = require('axios');

// Configuration
const API_GATEWAY_URL = 'http://localhost:3000';

// Test user data
const testUser = {
  email: '<EMAIL>',
  password: 'password123'
};

const assessmentData = {
  riasec: {
    realistic: 75,
    investigative: 85,
    artistic: 60,
    social: 50,
    enterprising: 70,
    conventional: 55
  },
  ocean: {
    conscientiousness: 65,
    extraversion: 55,
    agreeableness: 45,
    neuroticism: 30,
    openness: 80
  },
  viaIs: {
    creativity: 85,
    curiosity: 78,
    judgment: 70,
    loveOfLearning: 82,
    perspective: 60,
    bravery: 65,
    perseverance: 70,
    honesty: 75,
    zest: 60,
    love: 55,
    kindness: 68,
    socialIntelligence: 72,
    teamwork: 65,
    fairness: 70,
    leadership: 60,
    forgiveness: 55,
    humility: 50,
    prudence: 65,
    selfRegulation: 70,
    appreciationOfBeauty: 75,
    gratitude: 80,
    hope: 70,
    humor: 65,
    spirituality: 45
  }
};

let authToken = '';
let jobId = '';

// Helper function to make API calls with response tracking
async function makeRequest(method, url, data = null, headers = {}) {
  const startTime = Date.now();
  
  try {
    const config = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    const endTime = Date.now();
    
    return {
      success: true,
      status: response.status,
      data: response.data,
      responseTime: endTime - startTime,
      headers: response.headers
    };
  } catch (error) {
    const endTime = Date.now();
    
    return {
      success: false,
      status: error.response?.status || 500,
      data: error.response?.data || { message: error.message },
      responseTime: endTime - startTime,
      headers: error.response?.headers || {}
    };
  }
}

// Test functions
async function testSetupUser() {
  console.log('\n=== 1. SETUP TEST USER ===');
  
  // Try login first
  let result = await makeRequest('POST', `${API_GATEWAY_URL}/api/auth/login`, testUser);
  
  if (!result.success) {
    // Register if login fails
    result = await makeRequest('POST', `${API_GATEWAY_URL}/api/auth/register`, testUser);
  }
  
  console.log('Auth Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('Response Time:', `${result.responseTime}ms`);
  
  if (result.success && result.data.data?.token) {
    authToken = result.data.data.token;
    console.log('Auth token saved');
    return true;
  }
  
  return false;
}

async function testSubmitAssessment() {
  console.log('\n=== 2. SUBMIT ASSESSMENT ===');
  
  const result = await makeRequest('POST', `${API_GATEWAY_URL}/api/assessment/submit`, assessmentData, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('Submit Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('Response Time:', `${result.responseTime}ms`);
  
  if (result.success && result.data.data?.jobId) {
    jobId = result.data.data.jobId;
    console.log(`Job ID: ${jobId}`);
    return true;
  }
  
  return false;
}

async function testJobStatusMultipleTimes() {
  console.log('\n=== 3. TEST JOB STATUS MULTIPLE TIMES ===');
  
  const results = [];
  const numRequests = 5;
  
  for (let i = 1; i <= numRequests; i++) {
    console.log(`\n--- Request ${i}/${numRequests} ---`);
    
    const result = await makeRequest('GET', `${API_GATEWAY_URL}/api/archive/jobs/${jobId}`, null, {
      'Authorization': `Bearer ${authToken}`
    });
    
    console.log('Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
    console.log('Response Time:', `${result.responseTime}ms`);
    console.log('Content-Length:', result.headers['content-length'] || 'N/A');
    
    // Check for response consistency
    if (result.success) {
      const responseSize = JSON.stringify(result.data).length;
      console.log('Response Size:', `${responseSize} chars`);
      
      results.push({
        requestNumber: i,
        success: result.success,
        responseTime: result.responseTime,
        responseSize: responseSize,
        data: result.data
      });
    } else {
      console.log('Error:', result.data.error?.message || 'Unknown error');
    }
    
    // Wait 1 second between requests
    if (i < numRequests) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  return results;
}

async function analyzeResponseConsistency(results) {
  console.log('\n=== 4. ANALYZE RESPONSE CONSISTENCY ===');
  
  if (results.length === 0) {
    console.log('❌ No results to analyze');
    return false;
  }
  
  const firstResponse = results[0];
  let allConsistent = true;
  
  console.log('Response Analysis:');
  console.log(`  Total Requests: ${results.length}`);
  console.log(`  First Response Size: ${firstResponse.responseSize} chars`);
  
  // Check if all responses have the same size
  const sizes = results.map(r => r.responseSize);
  const uniqueSizes = [...new Set(sizes)];
  
  console.log(`  Unique Response Sizes: ${uniqueSizes.join(', ')}`);
  
  if (uniqueSizes.length === 1) {
    console.log('✅ All responses have consistent size');
  } else {
    console.log('❌ Response sizes are inconsistent');
    allConsistent = false;
  }
  
  // Check response times
  const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
  const maxResponseTime = Math.max(...results.map(r => r.responseTime));
  const minResponseTime = Math.min(...results.map(r => r.responseTime));
  
  console.log(`  Avg Response Time: ${avgResponseTime.toFixed(2)}ms`);
  console.log(`  Min Response Time: ${minResponseTime}ms`);
  console.log(`  Max Response Time: ${maxResponseTime}ms`);
  
  // Check for duplicate data in responses
  const firstDataStr = JSON.stringify(firstResponse.data);
  const allSameData = results.every(r => JSON.stringify(r.data) === firstDataStr);
  
  if (allSameData) {
    console.log('✅ All responses have identical data');
  } else {
    console.log('❌ Response data is inconsistent');
    allConsistent = false;
  }
  
  return allConsistent;
}

async function testResponseHeaders() {
  console.log('\n=== 5. TEST RESPONSE HEADERS ===');
  
  const result = await makeRequest('GET', `${API_GATEWAY_URL}/api/archive/jobs/${jobId}`, null, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('Headers Analysis:');
  console.log(`  Content-Type: ${result.headers['content-type'] || 'N/A'}`);
  console.log(`  Content-Length: ${result.headers['content-length'] || 'N/A'}`);
  console.log(`  Transfer-Encoding: ${result.headers['transfer-encoding'] || 'N/A'}`);
  console.log(`  Connection: ${result.headers['connection'] || 'N/A'}`);
  
  // Check for duplicate headers that might cause issues
  const suspiciousHeaders = ['x-powered-by', 'server'];
  suspiciousHeaders.forEach(header => {
    if (result.headers[header]) {
      console.log(`  ${header}: ${result.headers[header]}`);
    }
  });
  
  return result.success;
}

async function cleanup() {
  console.log('\n=== 6. CLEANUP ===');
  
  if (authToken) {
    const result = await makeRequest('DELETE', `${API_GATEWAY_URL}/api/auth/profile`, null, {
      'Authorization': `Bearer ${authToken}`
    });
    
    console.log('Cleanup Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  }
}

// Main test runner
async function runResponseDuplicationTests() {
  console.log('🚀 Starting Response Duplication Tests');
  console.log('======================================');

  try {
    // Setup
    const setupSuccess = await testSetupUser();
    if (!setupSuccess) {
      console.log('❌ Failed to setup user, aborting tests');
      return;
    }
    
    // Submit assessment to get a job
    const submitSuccess = await testSubmitAssessment();
    if (!submitSuccess) {
      console.log('❌ Failed to submit assessment, aborting tests');
      return;
    }
    
    // Wait a bit for job to be created
    console.log('\nWaiting 3 seconds for job to be processed...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Test multiple requests
    const results = await testJobStatusMultipleTimes();
    
    // Analyze consistency
    const isConsistent = await analyzeResponseConsistency(results);
    
    // Test headers
    await testResponseHeaders();
    
    // Cleanup
    await cleanup();
    
    // Summary
    console.log('\n🎯 TEST SUMMARY');
    console.log('===============');
    
    if (isConsistent) {
      console.log('✅ NO RESPONSE DUPLICATION DETECTED - Improvements working!');
    } else {
      console.log('❌ RESPONSE INCONSISTENCY DETECTED - Further investigation needed');
    }
    
  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
  }
}

// Run the tests
if (require.main === module) {
  runResponseDuplicationTests();
}

module.exports = {
  runResponseDuplicationTests,
  testSetupUser,
  testSubmitAssessment,
  testJobStatusMultipleTimes,
  analyzeResponseConsistency,
  cleanup
};
