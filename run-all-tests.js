#!/usr/bin/env node

/**
 * Master Test Runner for Profile Structure Improvements
 * Runs all tests to validate the fixes for profile structure and response duplication
 */

const { runProfileTests } = require('./test-profile-improvements');
const { runResponseDuplicationTests } = require('./test-response-duplication');

// Helper function to wait
function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Check if services are running
async function checkServices() {
  const axios = require('axios');
  const services = [
    { name: 'API Gateway', url: 'http://localhost:3000/health' },
    { name: 'Auth Service', url: 'http://localhost:3000/api/auth/health' },
    { name: 'Assessment Service', url: 'http://localhost:3000/api/assessment/health' },
    { name: 'Archive Service', url: 'http://localhost:3000/api/archive/health' }
  ];

  console.log('🔍 Checking Services...');
  console.log('========================');

  let allHealthy = true;

  for (const service of services) {
    try {
      const response = await axios.get(service.url, { timeout: 5000 });
      console.log(`✅ ${service.name}: HEALTHY`);
    } catch (error) {
      console.log(`❌ ${service.name}: UNHEALTHY (${error.message})`);
      allHealthy = false;
    }
  }

  return allHealthy;
}

// Main test runner
async function runAllTests() {
  console.log('🚀 ATMA Profile Structure Improvements - Full Test Suite');
  console.log('=========================================================');
  console.log('This test suite validates:');
  console.log('1. Profile structure improvements (school fields)');
  console.log('2. Response duplication fixes');
  console.log('3. API consistency and validation');
  console.log('');

  try {
    // Check if all services are running
    const servicesHealthy = await checkServices();
    if (!servicesHealthy) {
      console.log('\n⚠️  Some services are not healthy.');
      console.log('Please ensure all services are running:');
      console.log('- API Gateway (port 3000)');
      console.log('- Auth Service (port 3001)');
      console.log('- Assessment Service (port 3003)');
      console.log('- Archive Service (port 3002)');
      console.log('');
      console.log('Start services with: npm run dev (in each service directory)');
      return;
    }

    console.log('\n✅ All services are healthy, proceeding with tests...\n');

    // Test 1: Profile Structure Improvements
    console.log('📋 TEST SUITE 1: Profile Structure Improvements');
    console.log('================================================');
    await runProfileTests();

    // Wait between test suites
    console.log('\n⏳ Waiting 5 seconds before next test suite...');
    await wait(5000);

    // Test 2: Response Duplication
    console.log('\n📋 TEST SUITE 2: Response Duplication Tests');
    console.log('===========================================');
    await runResponseDuplicationTests();

    // Final Summary
    console.log('\n🎉 ALL TEST SUITES COMPLETED');
    console.log('============================');
    console.log('');
    console.log('Review the results above to ensure:');
    console.log('✅ Profile structure improvements are working');
    console.log('✅ School field validation is enforced');
    console.log('✅ Response duplication is resolved');
    console.log('✅ API responses are consistent');
    console.log('');
    console.log('If any tests failed, check the implementation and try again.');

  } catch (error) {
    console.error('\n💥 Test suite execution failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Command line interface
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    console.log('ATMA Profile Structure Improvements - Test Suite');
    console.log('');
    console.log('Usage:');
    console.log('  node run-all-tests.js                 - Run all test suites');
    console.log('  node run-all-tests.js --profile       - Run only profile tests');
    console.log('  node run-all-tests.js --response      - Run only response tests');
    console.log('  node run-all-tests.js --help          - Show this help');
    console.log('');
    console.log('Prerequisites:');
    console.log('  - All ATMA services must be running');
    console.log('  - Database must be accessible');
    console.log('  - Network connectivity to localhost');
  } else if (args.includes('--profile')) {
    console.log('🚀 Running Profile Structure Tests Only');
    console.log('=======================================');
    runProfileTests();
  } else if (args.includes('--response')) {
    console.log('🚀 Running Response Duplication Tests Only');
    console.log('==========================================');
    runResponseDuplicationTests();
  } else {
    runAllTests();
  }
}

module.exports = {
  runAllTests,
  checkServices
};
