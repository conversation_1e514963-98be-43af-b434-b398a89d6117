const axios = require('axios');

// Configuration
const API_GATEWAY_URL = 'http://localhost:3000';

// Test user data
const testUser = {
  email: '<EMAIL>',
  password: 'password123'
};

let authToken = '';

// Helper function to make API calls
async function makeRequest(method, url, data = null, headers = {}) {
  try {
    const config = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return {
      success: true,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    return {
      success: false,
      status: error.response?.status || 500,
      data: error.response?.data || { message: error.message }
    };
  }
}

// Test functions
async function testRegisterUser() {
  console.log('\n=== 1. REGISTER TEST USER ===');
  
  const result = await makeRequest('POST', `${API_GATEWAY_URL}/api/auth/register`, testUser);
  
  console.log('Registration Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  
  if (result.success && result.data.data?.token) {
    authToken = result.data.data.token;
    console.log('Auth token saved');
  } else {
    // Try login if user already exists
    console.log('Trying login instead...');
    const loginResult = await makeRequest('POST', `${API_GATEWAY_URL}/api/auth/login`, testUser);
    if (loginResult.success && loginResult.data.data?.token) {
      authToken = loginResult.data.data.token;
      console.log('Login successful, auth token saved');
    }
  }
  
  return !!authToken;
}

async function testProfileWithSchoolId() {
  console.log('\n=== 2. TEST PROFILE WITH SCHOOL_ID ===');
  
  const profileData = {
    full_name: 'Test User Profile',
    school_id: 1,
    date_of_birth: '1995-01-01',
    gender: 'male'
  };
  
  const result = await makeRequest('PUT', `${API_GATEWAY_URL}/api/auth/profile`, profileData, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('Update Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  
  if (result.success) {
    const profile = result.data.data.user.profile;
    console.log('Profile Data:');
    console.log(`  school_id: ${profile.school_id}`);
    console.log(`  school_origin: ${profile.school_origin}`);
    console.log(`  school_info.type: ${profile.school_info?.type}`);
    console.log(`  school_info.school_id: ${profile.school_info?.school_id}`);
    console.log(`  school_info.school_origin: ${profile.school_info?.school_origin}`);
    
    // Validate
    if (profile.school_id === 1 && profile.school_origin === null && profile.school_info?.type === 'structured') {
      console.log('✅ School ID test PASSED');
      return true;
    } else {
      console.log('❌ School ID test FAILED');
      return false;
    }
  }
  
  return false;
}

async function testProfileWithSchoolOrigin() {
  console.log('\n=== 3. TEST PROFILE WITH SCHOOL_ORIGIN ===');
  
  const profileData = {
    full_name: 'Test User Profile',
    school_origin: 'SMA Manual Test',
    date_of_birth: '1995-01-01',
    gender: 'male'
  };
  
  const result = await makeRequest('PUT', `${API_GATEWAY_URL}/api/auth/profile`, profileData, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('Update Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  
  if (result.success) {
    const profile = result.data.data.user.profile;
    console.log('Profile Data:');
    console.log(`  school_id: ${profile.school_id}`);
    console.log(`  school_origin: ${profile.school_origin}`);
    console.log(`  school_info.type: ${profile.school_info?.type}`);
    console.log(`  school_info.school_id: ${profile.school_info?.school_id}`);
    console.log(`  school_info.school_origin: ${profile.school_info?.school_origin}`);
    
    // Validate
    if (profile.school_id === null && profile.school_origin === 'SMA Manual Test' && profile.school_info?.type === 'manual') {
      console.log('✅ School Origin test PASSED');
      return true;
    } else {
      console.log('❌ School Origin test FAILED');
      return false;
    }
  }
  
  return false;
}

async function testProfileConflictValidation() {
  console.log('\n=== 4. TEST CONFLICT VALIDATION ===');
  
  const profileData = {
    full_name: 'Test User Profile',
    school_id: 1,
    school_origin: 'SMA Manual Test',
    date_of_birth: '1995-01-01',
    gender: 'male'
  };
  
  const result = await makeRequest('PUT', `${API_GATEWAY_URL}/api/auth/profile`, profileData, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('Update Status:', result.success ? '❌ UNEXPECTED SUCCESS' : '✅ EXPECTED FAILURE');
  
  if (!result.success) {
    console.log('Error Message:', result.data.error?.message || 'Unknown error');
    
    // Check if it's the expected validation error
    if (result.status === 400 && result.data.error?.code === 'VALIDATION_ERROR') {
      console.log('✅ Conflict validation test PASSED');
      return true;
    } else {
      console.log('❌ Conflict validation test FAILED - Wrong error type');
      return false;
    }
  } else {
    console.log('❌ Conflict validation test FAILED - Should have failed');
    return false;
  }
}

async function testGetProfile() {
  console.log('\n=== 5. TEST GET PROFILE ===');
  
  const result = await makeRequest('GET', `${API_GATEWAY_URL}/api/auth/profile`, null, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('Get Profile Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  
  if (result.success) {
    const profile = result.data.data.user.profile;
    console.log('Profile Structure:');
    console.log(`  Has school_info: ${!!profile.school_info}`);
    console.log(`  school_info.type: ${profile.school_info?.type}`);
    
    if (profile.school_info && ['structured', 'manual'].includes(profile.school_info.type)) {
      console.log('✅ Profile structure test PASSED');
      return true;
    } else {
      console.log('❌ Profile structure test FAILED');
      return false;
    }
  }
  
  return false;
}

async function cleanup() {
  console.log('\n=== 6. CLEANUP ===');
  
  if (authToken) {
    const result = await makeRequest('DELETE', `${API_GATEWAY_URL}/api/auth/profile`, null, {
      'Authorization': `Bearer ${authToken}`
    });
    
    console.log('Cleanup Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  }
}

// Main test runner
async function runProfileTests() {
  console.log('🚀 Starting Profile Structure Improvement Tests');
  console.log('================================================');

  const results = [];

  try {
    // Run all tests
    results.push(await testRegisterUser());
    results.push(await testProfileWithSchoolId());
    results.push(await testProfileWithSchoolOrigin());
    results.push(await testProfileConflictValidation());
    results.push(await testGetProfile());
    
    // Cleanup
    await cleanup();
    
    // Summary
    console.log('\n🎯 TEST SUMMARY');
    console.log('===============');
    
    const passed = results.filter(r => r).length;
    const total = results.length;
    
    console.log(`Tests Passed: ${passed}/${total}`);
    
    if (passed === total) {
      console.log('✅ ALL TESTS PASSED - Profile improvements working correctly!');
    } else {
      console.log('❌ SOME TESTS FAILED - Please check the implementation');
    }
    
  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
  }
}

// Run the tests
if (require.main === module) {
  runProfileTests();
}

module.exports = {
  runProfileTests,
  testRegisterUser,
  testProfileWithSchoolId,
  testProfileWithSchoolOrigin,
  testProfileConflictValidation,
  testGetProfile,
  cleanup
};
